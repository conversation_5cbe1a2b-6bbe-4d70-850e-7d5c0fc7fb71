package jarinker.cli.cmd;

import com.sun.tools.jdeps.JdepsFilter;
import jarinker.core.AnalyzerType;
import jarinker.core.DependencyGraph;
import jarinker.core.JdepsAnalyzer;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.jspecify.annotations.Nullable;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

/**
 * Analyze command for dependency analysis.
 *
 * <AUTHOR>
 */
@Command(description = "Analyze dependencies and generate dependency graph", mixinStandardHelpOptions = true)
public class AnalyzeCommand implements Runnable {

    @Parameters(description = "Source artifacts to analyze (JAR files or class directories)", arity = "1..*")
    private List<Path> sources;

    @Option(
            names = {"-cp", "-classpath", "--class-path"},
            description = "Classpath entries (can be specified multiple times)",
            required = true)
    private List<Path> classpath;

    // === jdeps options ===

    @Option(
            names = {"--type"},
            description = "Analysis type (class, package, module), see jarinker.core.AnalyzerType",
            defaultValue = "package")
    private AnalyzerType type;

    // Filter options
    @Option(
            names = {"--regex"},
            description = "Find dependencies matching the given pattern")
    private @Nullable Pattern regex;

    // Source filters
    @Option(
            names = {"--include-pattern"},
            description = "Restrict analysis to classes matching pattern")
    private @Nullable Pattern includePattern;

    // === jdeps options end ===

    @Option(
            names = {"--show-jdk-deps"},
            defaultValue = "false",
            description = "Show JDK dependencies, by default they are filtered out")
    private Boolean showJdkDeps;

    @Override
    @SneakyThrows
    public void run() {

        DependencyGraph graph;

        try (var jdepsConfiguration = JdepsAnalyzer.buildJdepsConfiguration(sources, classpath, Runtime.version())) {
            var analyzer = JdepsAnalyzer.builder()
                    .jdepsFilter(buildJdepsFilter())
                    .jdepsConfiguration(jdepsConfiguration)
                    .type(type)
                    .build();

            graph = analyzer.analyze();
        }

        // Print results
        printReport(graph);
    }

    /**
     * Build JdepsFilterBuilder with all configured options.
     *
     * @return configured JdepsFilterBuilder
     */
    private JdepsFilter buildJdepsFilter() {
        var filterBuilder = new JdepsFilter.Builder();

        if (regex != null) {
            filterBuilder.regex(regex);
        }

        filterBuilder.filter(false, false);

        filterBuilder.findJDKInternals(false);

        filterBuilder.findMissingDeps(false);

        if (includePattern != null) {
            filterBuilder.includePattern(includePattern);
        }

        return filterBuilder.build();
    }

    private void printReport(DependencyGraph graph) {
        switch (graph.getAnalysisType()) {
            case CLASS -> printReportForClass(graph);
            case PACKAGE -> printReportForPackage(graph);
            case MODULE -> printReportForModule(graph);
        }
    }

    private void printReportForModule(DependencyGraph graph) {
        printHeader("Module Dependency Analysis");
        printDependenciesByType(graph);
        System.out.println();
        printSummaryStats(graph);
    }

    private void printReportForClass(DependencyGraph graph) {
        printHeader("Class Dependency Analysis");
        printDependenciesByType(graph);
        System.out.println();
        printSummaryStats(graph);
    }

    private void printReportForPackage(DependencyGraph graph) {
        printHeader("Package Dependency Analysis");
        printDependenciesByType(graph);
        System.out.println();
        printSummaryStats(graph);
    }

    private void printHeader(String title) {
        System.out.println("╭─" + "─".repeat(title.length()) + "─╮");
        System.out.println("│ " + title + " │");
        System.out.println("╰─" + "─".repeat(title.length()) + "─╯");
        System.out.println();
    }

    private void printSummaryStats(DependencyGraph graph) {
        var dependenciesMap = graph.getDependenciesMap();
        String nodeTypePlural = getNodeTypePlural();

        // Calculate statistics
        int totalNodes = graph.getNodeCount();
        int usedNodes = 0;
        var showJdkDeps = this.showJdkDeps;

        for (var entry : dependenciesMap.entrySet()) {
            Set<String> deps = entry.getValue().stream()
                    .filter(dep -> showJdkDeps || !isJdkDependency(dep))
                    .filter(dep -> !isSameScopeAsSelf(entry.getKey(), dep))
                    .collect(Collectors.toSet());

            if (!deps.isEmpty()) {
                usedNodes++;
            }
        }

        int unusedNodes = totalNodes - usedNodes;
        double usageRate = totalNodes > 0 ? (double) usedNodes / totalNodes * 100 : 0.0;
        double unusedRate = totalNodes > 0 ? (double) unusedNodes / totalNodes * 100 : 0.0;

        System.out.println("📊 Statistics:");
        System.out.println("   • Total " + nodeTypePlural + ": " + totalNodes);
        System.out.printf("   • Used " + nodeTypePlural + ": %d (%.2f%%)\n", usedNodes, usageRate);
        System.out.printf("   • Unused " + nodeTypePlural + ": %d (%.2f%%)\n", unusedNodes, unusedRate);
    }

    private void printDependenciesByType(DependencyGraph graph) {
        var dependenciesMap = graph.getDependenciesMap();

        if (dependenciesMap.isEmpty()) {
            System.out.println("🔍 No dependencies found.");
            return;
        }

        System.out.println("🌳 Dependencies:");
        System.out.println();

        printDependencyTree(dependenciesMap, graph.getAnalysisType());
    }

    private void printDependencyTree(Map<String, Set<String>> dependenciesMap, AnalyzerType analysisType) {
        dependenciesMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> printNodeDependencies(entry.getKey(), entry.getValue(), analysisType));
    }

    private void printNodeDependencies(String source, Set<String> dependencies, AnalyzerType analysisType) {
        Set<String> filteredDependencies = filterDependencies(source, dependencies);

        if (!filteredDependencies.isEmpty()) {
            System.out.println("📦 " + source);
            printDependencyList(filteredDependencies);
            System.out.println();
        }
    }

    private Set<String> filterDependencies(String source, Set<String> dependencies) {
        return dependencies.stream()
                .filter(dep -> showJdkDeps || !isJdkDependency(dep))
                .filter(dep -> !isSameScopeAsSelf(source, dep))
                .collect(Collectors.toSet());
    }

    private void printDependencyList(Set<String> dependencies) {
        List<String> sortedDeps = dependencies.stream().sorted().toList();

        for (int i = 0; i < sortedDeps.size(); i++) {
            String dep = sortedDeps.get(i);
            boolean isLast = (i == sortedDeps.size() - 1);
            String prefix = isLast ? "   └─ " : "   ├─ ";
            String displayDep = formatDependencyName(dep);
            System.out.println(prefix + displayDep);
        }
    }

    private String formatDependencyName(String fullName) {
        // For all analysis types, show only the name part (after the last "/")
        // This removes jar prefixes like "guava-33.4.8-jre.jar/" or "not found/"
        if (fullName.contains("/")) {
            return fullName.substring(fullName.lastIndexOf("/") + 1);
        }
        return fullName;
    }

    private boolean isSameScopeAsSelf(String source, String dependency) {
        return switch (type) {
            case PACKAGE -> // For package analysis, filter dependencies within the same package
                extractPackageName(source).equals(extractPackageName(dependency));
            case MODULE -> // For module analysis, filter dependencies within the same module
                extractModuleName(source).equals(extractModuleName(dependency));
            case CLASS -> // For class analysis, don't filter same scope dependencies
                false;
        };
    }

    /**
     * Extract package name from a node name.
     * For package analysis, the format is typically "archive/package.name"
     *
     * @param nodeName the node name
     * @return the package name
     */
    private static String extractPackageName(String nodeName) {
        // Handle format like "quick-start-0.1.0.jar/com.example"
        int slashIndex = nodeName.indexOf('/');
        if (slashIndex != -1) {
            return nodeName.substring(slashIndex + 1);
        }
        return nodeName;
    }

    /**
     * Extract module name from a node name.
     * For module analysis, the format is typically "archive/module.name"
     *
     * @param nodeName the node name
     * @return the module name
     */
    private static String extractModuleName(String nodeName) {
        // Handle format like "quick-start-0.1.0.jar/module.name"
        int slashIndex = nodeName.indexOf('/');
        if (slashIndex != -1) {
            return nodeName.substring(slashIndex + 1);
        }
        return nodeName;
    }

    private String getNodeTypePlural() {
        return switch (type) {
            case PACKAGE -> "packages";
            case MODULE -> "modules";
            case CLASS -> "classes";
        };
    }

    /**
     * Check if a dependency is a JDK internal dependency that should be filtered out.
     *
     * @param dependency the dependency name
     * @return true if it's a JDK dependency
     */
    private static boolean isJdkDependency(String dependency) {
        return dependency.startsWith("java.")
                || dependency.startsWith("javax.")
                || dependency.startsWith("jdk.")
                || dependency.startsWith("sun.")
                || dependency.startsWith("com.sun.")
                || dependency.contains("JDK removed internal API");
    }
}
