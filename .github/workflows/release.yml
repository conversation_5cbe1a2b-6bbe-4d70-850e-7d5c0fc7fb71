name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Check version
        run: |
          tag=${GITHUB_REF#refs/tags/v}
          PROJECT_VERSION=$(grep "^version=" gradle.properties | cut -d'=' -f2)
          if [[ "$PROJECT_VERSION" == "$tag" ]]; then
            echo "Version match: tag $VERSION_TAG matches project version $PROJECT_VERSION, proceeding with release"
          else
            echo "Version mismatch: tag $VERSION_TAG does not match project version $PROJECT_VERSION"
            exit 1
          fi

      - name: Set up JDK
        uses: actions/setup-java@v5
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'gradle'

      - name: Build
        run: |
          ./gradlew build
          ./gradlew :jarinker-cli:installDist

      - name: Upload binaries
        uses: actions/upload-artifact@v4
        with:
          name: jarinker-cli-${GITHUB_REF#refs/tags/v}
          path: jarinker-cli/build/install/jarinker

  release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [ build ]
    steps:
      - name: Check out the repo
        uses: actions/checkout@v5

      - name: Download binaries
        uses: actions/download-artifact@v4
        with:
          pattern: 'jarinker-cli-*'

      - name: Show downloaded files
        run: tree jarinker-cli-${GITHUB_REF#refs/tags/v}

      - name: Extract tag
        run: |
          tag=${GITHUB_REF#refs/tags/v}
          echo "TAG=${tag}" >> $GITHUB_ENV

      - name: Prepare zipped binaries
        run: |
          
          mkdir -p zipped

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          generate_release_notes: true
          draft: true
          files: |
            zipped/
